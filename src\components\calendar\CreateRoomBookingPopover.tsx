'use client';

import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { DateTime } from 'luxon';
import { X, Calendar, Clock, MapPin, Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { SelectedSlot, CreateBookingFormData } from './types';

interface CreateRoomBookingPopoverProps {
  isOpen: boolean;
  onClose: () => void;
  selectedSlot: SelectedSlot | null;
  room: any; // The specific room for this calendar
  onSubmit: (data: CreateBookingFormData) => Promise<void>;
  isLoading?: boolean;
  position: { x: number; y: number };
}

export function CreateRoomBookingPopover({
  isOpen,
  onClose,
  selectedSlot,
  room,
  onSubmit,
  isLoading = false,
  position
}: CreateRoomBookingPopoverProps) {
  const [internalAttendees, setInternalAttendees] = useState<string[]>([]);
  const [externalAttendees, setExternalAttendees] = useState<string[]>([]);
  const [newInternalAttendee, setNewInternalAttendee] = useState('');
  const [newExternalAttendee, setNewExternalAttendee] = useState('');

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    reset,
    formState: { errors }
  } = useForm<CreateBookingFormData>();

  // Set default values when slot is selected
  useEffect(() => {
    if (selectedSlot && isOpen && room) {
      const startTime = DateTime.fromJSDate(selectedSlot.start).toFormat('HH:mm');
      const endTime = DateTime.fromJSDate(selectedSlot.end).toFormat('HH:mm');

      setValue('startTime', startTime);
      setValue('endTime', endTime);
      setValue('resourceId', room.id); // Set the room ID directly

      // Reset other fields
      setValue('title', '');
      setValue('description', '');
    }
  }, [selectedSlot, isOpen, room, setValue]);

  // Reset form when popover closes
  useEffect(() => {
    if (!isOpen) {
      reset();
      setInternalAttendees([]);
      setExternalAttendees([]);
      setNewInternalAttendee('');
      setNewExternalAttendee('');
    }
  }, [isOpen, reset]);

  // Functions to handle attendees
  const addInternalAttendee = () => {
    if (newInternalAttendee.trim() && !internalAttendees.includes(newInternalAttendee.trim())) {
      const updated = [...internalAttendees, newInternalAttendee.trim()];
      setInternalAttendees(updated);
      setNewInternalAttendee('');
    }
  };

  const addExternalAttendee = () => {
    if (newExternalAttendee.trim() && !externalAttendees.includes(newExternalAttendee.trim())) {
      const updated = [...externalAttendees, newExternalAttendee.trim()];
      setExternalAttendees(updated);
      setNewExternalAttendee('');
    }
  };

  const removeInternalAttendee = (attendee: string) => {
    setInternalAttendees(prev => prev.filter(a => a !== attendee));
  };

  const removeExternalAttendee = (attendee: string) => {
    setExternalAttendees(prev => prev.filter(a => a !== attendee));
  };

  const handleFormSubmit = async (data: CreateBookingFormData) => {
    try {
      const formData = {
        ...data,
        internalAttendees: internalAttendees.filter(email => email.trim() !== ''),
        externalAttendees: externalAttendees.filter(email => email.trim() !== '')
      };
      await onSubmit(formData);
    } catch (error) {
      // Error handling is done in parent component
    }
  };

  // Close popover when clicking outside
  useEffect(() => {
    if (!isOpen) return;

    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      const popover = document.getElementById('room-booking-popover');

      // Don't close if clicking on calendar elements or the popover itself
      if (popover && !popover.contains(target)) {
        // Check if click is on calendar elements
        const isCalendarClick = target.closest('.rbc-calendar') ||
          target.closest('.rbc-event') ||
          target.closest('.rbc-time-slot');

        if (!isCalendarClick) {
          onClose();
        }
      }
    };

    // Add delay to prevent immediate closing when opening
    const timer = setTimeout(() => {
      document.addEventListener('mousedown', handleClickOutside);
    }, 200);

    return () => {
      clearTimeout(timer);
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose]);

  // Close on Escape key
  useEffect(() => {
    if (!isOpen) return;

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [isOpen, onClose]);

  if (!isOpen || !selectedSlot || !room) return null;

  const selectedDate = DateTime.fromJSDate(selectedSlot.start);
  const formattedDate = selectedDate.toFormat('EEEE, dd \'de\' MMMM \'de\' yyyy');

  return (
    <div
      id="room-booking-popover"
      className="fixed z-[9999] w-96 bg-white rounded-lg shadow-2xl border border-gray-200"
      style={{
        left: `${position.x}px`,
        top: `${position.y}px`,
        maxHeight: 'min(500px, 80vh)',
        overflowY: 'auto',
        boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
      }}
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center gap-2">
          <Calendar className="h-5 w-5 text-purple-600" />
          <h3 className="text-lg font-semibold text-gray-900">Nueva Reserva</h3>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={onClose}
          className="h-8 w-8 p-0 hover:bg-gray-100"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>

      {/* Content */}
      <form onSubmit={handleSubmit(handleFormSubmit)} className="p-4 space-y-4">
        {/* Date and Time Info */}
        <div className="bg-purple-50 rounded-lg p-3 space-y-2">
          <div className="flex items-center gap-2 text-sm text-purple-700">
            <Calendar className="h-4 w-4" />
            <span className="font-medium">{formattedDate}</span>
          </div>
          <div className="flex items-center gap-2 text-sm text-purple-700">
            <Clock className="h-4 w-4" />
            <span>{watch('startTime')} - {watch('endTime')}</span>
          </div>
        </div>

        {/* Room Info - Display only, not selectable */}
        <div className="bg-gray-50 rounded-lg p-3">
          <div className="flex items-center gap-2 text-sm text-gray-700">
            <MapPin className="h-4 w-4" />
            <span className="font-medium">{room.name}</span>
            <span className="text-gray-500">(Capacidad: {room.capacity})</span>
          </div>
        </div>

        {/* Basic Info */}
        <div className="grid grid-cols-1 gap-4">
          <div>
            <Label htmlFor="title">Título de la reunión *</Label>
            <Input
              id="title"
              {...register('title', { required: 'El título es requerido' })}
              placeholder="Ej: Reunión de equipo"
            />
            {errors.title && (
              <p className="text-sm text-red-600 mt-1">{errors.title.message}</p>
            )}
          </div>

          <div className="grid grid-cols-2 gap-2">
            <div>
              <Label htmlFor="startTime">Hora inicio</Label>
              <Input
                id="startTime"
                type="time"
                {...register('startTime', { required: true })}
              />
            </div>
            <div>
              <Label htmlFor="endTime">Hora fin</Label>
              <Input
                id="endTime"
                type="time"
                {...register('endTime', { required: true })}
              />
            </div>
          </div>
        </div>

        {/* Description */}
        <div>
          <Label htmlFor="description">Descripción</Label>
          <Textarea
            id="description"
            {...register('description')}
            placeholder="Descripción opcional de la reunión"
            rows={2}
          />
        </div>

        {/* Attendees */}
        <div className="space-y-4">
          <h4 className="font-medium text-gray-900">Asistentes</h4>
          <Tabs defaultValue="internal" className="space-y-4">
            <TabsList className="grid w-full grid-cols-2 rounded-xl bg-gray-100">
              <TabsTrigger value="internal" className="rounded-lg">Internos</TabsTrigger>
              <TabsTrigger value="external" className="rounded-lg">Externos</TabsTrigger>
            </TabsList>

            <TabsContent value="internal" className="space-y-3">
              <div className="flex gap-2">
                <Input
                  value={newInternalAttendee}
                  onChange={(e) => setNewInternalAttendee(e.target.value)}
                  placeholder="Email del empleado..."
                  className="rounded-xl border-gray-200 focus:border-purple-500 focus:ring-purple-500"
                  onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addInternalAttendee())}
                />
                <Button
                  type="button"
                  onClick={addInternalAttendee}
                  variant="outline"
                  className="rounded-xl border-purple-200 text-purple-600 hover:bg-purple-50"
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              {internalAttendees.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {internalAttendees.map((attendee) => (
                    <div
                      key={attendee}
                      className="bg-blue-100 text-blue-700 hover:bg-blue-200 rounded-full px-3 py-1 flex items-center gap-2"
                    >
                      {attendee}
                      <button
                        type="button"
                        onClick={() => removeInternalAttendee(attendee)}
                        className="hover:text-blue-900"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </TabsContent>

            <TabsContent value="external" className="space-y-3">
              <div className="flex gap-2">
                <Input
                  value={newExternalAttendee}
                  onChange={(e) => setNewExternalAttendee(e.target.value)}
                  placeholder="Email del invitado externo..."
                  className="rounded-xl border-gray-200 focus:border-purple-500 focus:ring-purple-500"
                  onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addExternalAttendee())}
                />
                <Button
                  type="button"
                  onClick={addExternalAttendee}
                  variant="outline"
                  className="rounded-xl border-purple-200 text-purple-600 hover:bg-purple-50"
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              {externalAttendees.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {externalAttendees.map((attendee) => (
                    <div
                      key={attendee}
                      className="bg-green-100 text-green-700 hover:bg-green-200 rounded-full px-3 py-1 flex items-center gap-2"
                    >
                      {attendee}
                      <button
                        type="button"
                        onClick={() => removeExternalAttendee(attendee)}
                        className="hover:text-green-900"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </TabsContent>
          </Tabs>
        </div>

        {/* Actions */}
        <div className="flex gap-2 pt-2">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            className="flex-1"
            disabled={isLoading}
          >
            Cancelar
          </Button>
          <Button
            type="submit"
            className="flex-1 bg-purple-600 hover:bg-purple-700"
            disabled={isLoading}
          >
            {isLoading ? 'Creando...' : 'Crear Reserva'}
          </Button>
        </div>
      </form>
    </div>
  );
}
